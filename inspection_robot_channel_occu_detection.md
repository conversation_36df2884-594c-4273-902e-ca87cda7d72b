**技术交底书**

- 技术交底书的要求：

  应清楚、完整地写明发明的内容，使本行业的技术人员能够根据此内容再次实现本发明。

1. 基本信息
2. <a name="正文"></a>目前已有的与我司本专利技术最接近的现有技术及其缺陷

   通道占用检测对智能仓储、交通管理等领域的作业效率优化、安全风险防控、资源动态调度等具有重要应用价值。传统的通道占用监测方法主要依赖于摄像头监控和人工巡查，存在效率低下、实时性差、准确率低以及智能化程度低等不足。人工巡查不仅耗费人力，而且无法做到24小时实时监控。传统的基于图像处理的占用检测方法，对于复杂场景（如光照变化、遮挡、物体形态多样）的适应性差，容易产生误报和漏报。

   现有的一些基于深度学习的目标检测方法，如专利CN119479210A中提及的基于多源数据融合的检测方法、专利CN119445558A基于三维模型重建的方法、专利CN118942035A基于图像增强和修复的方法，虽然在一定程度上提升了检测精度，但仍存在计算复杂度高、模型泛化能力有限以及缺乏对占用物体的持续追踪等问题。多源数据融合和三维模型重建计算量大，对硬件设备要求高，难以满足实时性要求。对于特定场景训练的模型，在其他场景下可能性能下降。对于移动的占用物体，单一帧的检测结果不足以全面反映占用情况。

   为了解决现有技术存在的上述问题，本发明提出了一种基于YOLOv11与DeepSORT的通道占用监测方法。该方法旨在实现对通道内占用物体的实时、准确检测与持续跟踪，降低误报率和漏报率，同时降低算法的计算复杂度，使其能够在端侧平台上运行，并实现对占用事件的有效管理。

   现有技术附图：（如有现有技术的附图的，提供现有技术的附图）

   <a name="方案"></a>3、我司专利技术的附图说明及附图：

   ![patent-1.drawio](Aspose.Words.b6743577-24cc-4628-8fd8-db000db3fa8d.002.png)

**图1** 本发明的系统流程示意图

![patent-3.drawio](Aspose.Words.b6743577-24cc-4628-8fd8-db000db3fa8d.003.png)

**图2** YOLOv11模型网络框架图

![patent-2.drawio](Aspose.Words.b6743577-24cc-4628-8fd8-db000db3fa8d.004.png)

**图3** DeepSORT框架图

4、我司专利技术采用的技术方案：

4\.1、我司专利技术的发明点以及其与现有技术的区别点：

（注：列举出本发明相对于现有技术的区别点；区别点有多个的，请一一描述说明，并描述这些区别点的有益效果）

发明点：

1. 本发明设计了一种级联检测-追踪架构，将YOLOv11目标检测、DeepSORT目标跟踪、ROI区域判定和时间阈值判据相结合，实现了对通道占用状态的实时、准确、智能化的监测与预警。
1. 本发明采用YOLOv11作为目标检测器，并针对通道占用检测任务进行了优化。包括：针对复杂光照等场景多种数据增强方法。
1. 本发明采用DeepSORT算法进行目标跟踪，并针对通道占用场景进行了改进。包括：重训练的特征提取器MobileNetv2，提高追踪在遮挡情况下精度。

   区别点与效益：

1. 区别于传统的基于图像处理的通道占用检测方法易受光照、遮挡、阴影等因素影响，准确率低；一些仅进行单帧检测的方法无法处理移动的占用物体，容易漏检。本发明采用的级联检测-追踪架构，能够实现对通道占用物体的实时、准确检测与持续跟踪，有效降低误报率和漏报率。通过引入ROI区域和时间阈值判据，能够准确判断通道是否被占用。
1. 区别于一些基于多源数据融合或三维模型重建的目标检测方法计算复杂度高，不适合部署在端侧计算平台。本发明采用的YOLOv11是一种高效的单阶段目标检测算法，具有较高的检测精度和速度, 且经过了专门的优化, 能够在保证检测性能的同时，降低算法的计算复杂度，使其能够部署在资源受限的端侧计算平台。
1. 区别一些仅依靠运动信息进行跟踪的方法容易受到遮挡、光照变化等因素的影响，导致跟踪丢失或ID跳变；一些不进行跟踪的方法无法区分不同的占用物体，无法进行占用时长统计。本发明采用的DeepSORT算法在通道占用数据集上重训练了其特征提取器，基于结合的目标运动信息和外观特征、级联匹配策略等，提高了跟踪的准确性和鲁棒性，能够有效应对通道占用场景中的复杂环境。

   4\.2、结合附图详细描述我司专利技术方案：

   本发明提出了一种基于YOLOv11和DeepSORT的通道占用监测方法，其系统流程如图1所示。该方法首先通过摄像头获取通道的实时视频流（RTSP）。然后，利用YOLOv11目标检测模型对视频流中的每一帧图像进行处理，检测出潜在的占用物体（叉车、纸箱、物料箱、插板、推车），并输出每个物体的类别、置信度以及边界框坐标。接着，采用DeepSORT跟踪算法对YOLOv11检测到的目标进行跨帧关联和跟踪，为每个目标分配唯一的ID，并在后续帧中持续更新其位置和状态。DeepSORT算法通过结合目标的运动信息（位置、速度等）和外观特征（通过MobileNetv2网络提取）来提高跟踪的准确性和鲁棒性。在获取到目标的跟踪轨迹后，系统会根据预先定义的感兴趣区域（Region of Interest, ROI，即通道区域）进行空间判据，判断目标是否位于通道内。如果目标进入ROI区域，则启动时间累积器，记录目标在ROI区域内的停留时间。最后，进行超阈值判断，将累积的停留时间与预设的时间阈值进行比较，如果停留时间超过阈值，则判定为通道占用事件，系统触发报警（消息推送），并记录相关信息（占用时间、占用物体类别、位置等）。

   **4.2.1**本发明采用YOLOv11作为目标检测器，其网络结构如图2所示。YOLOv11是一种高效的单阶段目标检测算法，它将目标检测问题转化为一个回归问题，直接预测目标的类别和位置。YOLOv11的网络结构主要由Backbone（主干网络）、Neck（颈部网络）和Head（头部网络）三部分组成：Backbone负责从输入图像中提取特征，由一系列CBS模块（卷积层、批归一化层、SiLU激活函数）和C3K2模块（一种高效的特征提取模块）串联而成。Backbone的末端设置了SPPF模块（空间金字塔池化）和C2PSA模块（上下文感知和通道级的金字塔空间注意力模块），用于增强网络对不同尺度目标的感知能力和特征的判别性。Neck位于Backbone和Head之间，采用FPN（特征金字塔网络）结构，通过上采样和特征拼接操作，融合来自Backbone不同层级的特征图，以获得多尺度的特征表示。Head基于Neck输出的多尺度融合特征图，进行最终的目标检测和定位。Head网络主要由CBS模块、DSC模块（深度可分离卷积）和Conv2d模块组成。Head网络通常包含多个并行的检测分支，每个分支负责检测不同尺度的目标。

   **4.2.2**如图3所示，本发明所采用的DeepSORT跟踪算法，其流程主要包括以下步骤：首先，系统输入视频帧，作为跟踪算法的输入数据。随后，利用目标检测器（YOLOv11）对输入的视频帧进行目标检测，以获取当前帧中所有被检测目标的检测框位置信息。同时，对检测到的目标，基于MobileNetv2进行深度特征提取，提取每个目标的外观特征向量，用于后续的数据关联。对于已有的跟踪轨迹，利用卡尔曼滤波算法进行状态预测。卡尔曼滤波通过预测和更新两个步骤，估计目标在当前帧的位置和状态信息，为数据关联提供预测基础。接着，采用匈牙利算法进行数据关联。数据关联的核心是构建代价矩阵，该矩阵的元素表示检测目标与预测轨迹之间的匹配程度。匹配代价综合考虑了运动信息和外观信息，运动信息通过马氏距离度量，外观信息则通过余弦距离度量。匈牙利算法旨在解决指派问题，在代价矩阵中找到最优匹配方案。为提高数据关联的准确性和鲁棒性，本发明进一步采用级联匹配策略。级联匹配通过多层级的匹配过程，优先考虑外观特征相似度高的匹配，并逐步放宽匹配条件，从而更有效地处理目标遮挡、消失和重新出现等复杂情况。最后，通过轨迹管理模块，对跟踪轨迹进行更新和维护，包括新轨迹的创建、已有轨迹的更新以及轨迹生命周期的管理，最终输出跟踪结果，包含每个被跟踪目标的唯一身份标识及其在视频帧中的运动轨迹信息。关键公式如下。

1. 卡尔曼滤波 - 预测步骤:

   x^k|k−1=Fkx^k−1|k−1+Bkuk

   Pk|k−1=FkPk−1|k−1FkT+Qk

   上述公式描述了卡尔曼滤波的预测步骤，用于估计目标在当前时刻的状态x^k|k−1和协方差Pk|k−1。其中，x^k−1|k−1和Pk−1|k−1是上一时刻的状态估计和协方差，Fk是状态转移矩阵，Bk是控制输入矩阵，uk是控制向量，Qk是过程噪声协方差矩阵。

1. 马氏距离

   dmotion(i,j)=(di−Hx^j|j−1)TSj−1(di−Hx^j|j−1)

   公式用于计算第i个检测目标与第j个预测轨迹之间的运动信息代价，即马氏距离。di为检测框中心，x^j|j−1为预测轨迹的状态向量，H是观测矩阵，Sj为协方差矩阵。

1. 余弦距离

   dappearance(i,j)=1−fi⋅fj||i|⋅||j|

   该公式用于计算第i个检测目标与第j个预测轨迹之间的外观信息代价，即余弦距离。fi和fj分别为检测目标和预测轨迹的外观特征向量。

   **4.2.3**在本发明的深度学习模型在训练中，为了提高模型在复杂场景下的泛化能力,采用多种数据增强方法:

1) 对图像进行mosaic数据增强；概率设置为1.0。
1) 对图像进行左右翻转、随机裁剪；概率设置为0.5。
1) 对图像亮度进行50提升；概率设置为0.5
1) 对图像的对比度、饱和度、色调色相进行随机调整。

   **4.2.4**模型训练相关配置：

1. 模型训练采用Anchor-free无锚框方式和SimOTA 标签分配策略；
1. 模型训练中使用Adam作为优化器；
1. 模型的评价指标采用mAP进行衡量。


   5、我司专利技术中相关名词的解释

   mAP (mean Average Precision): 平均精度均值，是目标检测算法常用的评价指标，用于衡量算法的检测精度。

   IoU (Intersection over Union): 交并比，用于衡量预测边界框和真实边界框之间的重叠程度。

   RTSP (Real Time Streaming Protocol): 实时流传输协议，用于传输实时音视频流。

   ROI (Region of Interest): 感兴趣区域，在本发明中指预先定义的通道区域。

   Anchor-free: 无锚框. 一种目标检测方法,不需要预先定义的锚框.

   SimOTA: 一种标签分配策略.

   卡尔曼滤波: 一种利用一系列观测值,估计动态系统状态的算法.

   匈牙利算法: 一种求解二分图最大权匹配的算法.

   马氏距离: 一种考虑数据分布的距离度量.

   余弦距离: 一种度量两个向量之间相似度的指标.
